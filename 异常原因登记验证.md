我想使用python实现从PG数据库读取"异常原因登记"，通过访问 [https://www.tiangong.cn/](https://www.tiangong.cn/) 天工AI搜索"根据异常现象对异常原因进行批判性分析"。

操作步骤如下：

## 1、PG数据库查询异常原因登记

```
with td_workorder_item_detail as (
	select  point_id,item_id,order_id,num ,biz_code
	    ,case when biz_code ='EHS-JC-02' then CAST(PARSE_JSON( value) AS ARRAY<STRING>)  end AS time_strings
	    ,case when biz_code <>'EHS-JC-02' then COALESCE (name,value) end  biz_info
	    ,end_at
	from ads.ads_workorder_item_detail awid 
	where biz_code in  ('EHS-JC-05','EHS-JC-01','EHS-JC-04','EHS-JC-06','EHS-JC-07','EHS-JC-02')
	   and end_at >days_sub(now(),7)
	order by point_id,item_id,order_id,num asc,biz_code 
)
select   point_id,item_id,order_id,num
   ,max(case when biz_code='EHS-JC-01'  then  biz_info end)  因子
   ,STR_TO_DATE(max(ARRAY_SLICE(time_strings, 1, 1)[1]) , '%Y/%m/%d %H:%i') 开始时间
   ,STR_TO_DATE(max(ARRAY_SLICE(time_strings, 2, 1)[1]), '%Y/%m/%d %H:%i')  结束时间
   ,max(case when biz_code='EHS-JC-07'  then  biz_info end)      异常类型
   ,max(case when biz_code='EHS-JC-04'  then  biz_info end)      异常情况
   ,max(case when biz_code='EHS-JC-05'  then  biz_info end)      异常原因及处理
   ,max(case when biz_code='EHS-JC-06'  then  biz_info end)     处理结果
   ,max(end_at)     处理日期
  from td_workorder_item_detail  
group by  point_id,item_id,order_id,num 
order by  point_id,item_id,order_id,num 
;

```

SQL

## 2、遍历每一个异常原因，以json格式输出，例如

```
{ "因子" : "["化学需氧量"]", "开始时间" : "2025-07-24 14:00:00.0", "结束时间" : "2025-07-25 12:00:00.0", "异常类型" : "设备故障", "异常情况" : "数据不变", "异常原因及处理" : "检查后发现是压力传感报警，处理清洗后恢复正常", "处理结果" : "恢复正常", "处理日期" : "2025-07-25 11:48:23.0" }
```

JSON

## 3、访问 [https://www.tiangong.cn/](https://www.tiangong.cn/) 天工AI搜索"根据异常现象对异常原因进行批判性分析"。

```
{ "因子" : "["化学需氧量"]", "开始时间" : "2025-07-24 14:00:00.0", "结束时间" : "2025-07-25 12:00:00.0", "异常类型" : "设备故障", "异常情况" : "数据不变", "异常原因及处理" : "检查后发现是压力传感报警，处理清洗后恢复正常", "处理结果" : "恢复正常", "处理日期" : "2025-07-25 11:48:23.0" }

以上记录是异常现象（"因子"，"开始时间"，"开始时间"，"异常类型"，"异常情况"）、异常原因（"异常原因及处理"），异常处理的处理情况（"处理结果"，"处理日期"） ，由于异常原因是人为登记，会存在主观意识，可能跟实际造成具体原因不匹配，请根据异常现象对异常原因进行批判性分析，输出内容为 1、可信方面 2、批判性风险（主观登记问题） 2、是否合理，是/否 3、存在的潜在异常原因 4、异常现象的合规性 5、补充建议

注意： "异常原因及处理"是人为登记，因子名称不是按照规范，例如tn：总氮，tp：总磷，cod：化学需氧量，ph：pH值
```

Plain Text

## 4、搜索内容回写PG数据库

把天工AI搜索回复的内容 1、可信方面 2、批判性风险（主观登记问题） 2、是否合理，是/否 3、存在的潜在异常原因 4、异常现象的合规性 5、补充建议 写入到PG数据库中。

# 技术难点

-  [https://www.tiangong.cn/](https://www.tiangong.cn/)  天工AI搜索 没有提供API，而且AI思考时间长，如何实现AI搜索，是使用 如chrome-mcp 或者Playwright mcp ？

请提供设计方案，要求功能模块化，方便后续功能扩展